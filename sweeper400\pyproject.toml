[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sweeper400"
version = "0.1.0"
description = "协同控制NI数据采集卡和步进电机的自动化测量包"
authors = [
    {name = "400&402"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "numpy",
    "scipy",
    "matplotlib",
    "nidaqmx",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["sweeper400*"]



[tool.black]

# 设置行宽（Black 默认88，但现代屏幕也可适当放宽）
# line-length = 120

# 明确指定Python版本，启用最新语法兼容
target-version = ["py312"]

# 强制统一引号风格（单引号优先）
skip-string-normalization = false

# 启用实验性风格改进（Black 23+版本支持）
preview = true