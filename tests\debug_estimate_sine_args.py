"""
调试 estimate_sine_args 函数的检测精度问题
"""

import numpy as np
from sweeper400.analyze.basic_sine import estimate_sine_args, get_sine
from sweeper400.analyze.my_dtypes import init_sampling_info, init_sine_args


def debug_estimate_sine_args():
    """调试函数检测精度"""
    # 测试参数
    sampling_rate = 1000.0
    samples_num = 2048
    true_frequency = 50.0
    true_amplitude = 2.0
    true_phase = 0.5

    print(f"真实参数:")
    print(f"  频率: {true_frequency} Hz")
    print(f"  幅值: {true_amplitude}")
    print(f"  相位: {true_phase} rad ({true_phase*180/np.pi:.1f}°)")
    print(f"  采样率: {sampling_rate} Hz")
    print(f"  采样点数: {samples_num}")
    print()

    # 生成测试信号
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    true_sine_args = init_sine_args(true_frequency, true_amplitude, true_phase)
    test_waveform = get_sine(sampling_info, true_sine_args)

    print(f"生成的波形信息:")
    print(f"  波形形状: {test_waveform.shape}")
    print(f"  波形最大值: {np.max(test_waveform):.6f}")
    print(f"  波形最小值: {np.min(test_waveform):.6f}")
    print(f"  波形RMS: {np.sqrt(np.mean(test_waveform**2)):.6f}")
    print()

    # 进行估计
    print("开始参数估计...")

    # 设置日志级别为DEBUG以查看详细信息
    import logging
    from sweeper400.logger import get_logger

    logger = get_logger("sweeper400.analyze.basic_sine")
    logger.setLevel(logging.DEBUG)

    estimated_args = estimate_sine_args(test_waveform, approx_freq=true_frequency)

    print(f"估计结果:")
    print(f"  频率: {estimated_args['frequency']:.6f} Hz")
    print(f"  幅值: {estimated_args['amplitude']:.6f}")
    print(
        f"  相位: {estimated_args['phase']:.6f} rad ({estimated_args['phase']*180/np.pi:.1f}°)"
    )
    print()

    # 计算误差
    freq_error = abs(estimated_args["frequency"] - true_frequency)
    amp_error = abs(estimated_args["amplitude"] - true_amplitude)
    phase_error = abs(estimated_args["phase"] - true_phase)

    print(f"误差分析:")
    print(f"  频率误差: {freq_error:.6f} Hz ({freq_error/true_frequency*100:.4f}%)")
    print(f"  幅值误差: {amp_error:.6f} ({amp_error/true_amplitude*100:.4f}%)")
    print(f"  相位误差: {phase_error:.6f} rad ({phase_error*180/np.pi:.4f}°)")
    print()

    # 分析频率分辨率
    freq_resolution = sampling_rate / samples_num
    print(f"频率分辨率: {freq_resolution:.6f} Hz")
    print(f"频率误差相对于分辨率: {freq_error/freq_resolution:.2f} bins")
    print()

    # 验证生成的信号是否正确
    time_array = np.arange(samples_num) / sampling_rate
    expected_signal = true_amplitude * np.sin(
        2 * np.pi * true_frequency * time_array + true_phase
    )
    signal_diff = np.max(np.abs(test_waveform - expected_signal))
    print(f"生成信号验证:")
    print(f"  与期望信号的最大差异: {signal_diff:.10f}")
    print()

    # 手动FFT分析
    print("手动FFT分析:")
    fft_result = np.fft.fft(test_waveform)
    fft_freqs = np.fft.fftfreq(samples_num, 1.0 / sampling_rate)

    # 只取正频率部分
    positive_freq_mask = fft_freqs >= 0
    fft_result_positive = fft_result[positive_freq_mask]
    fft_freqs_positive = fft_freqs[positive_freq_mask]
    fft_magnitude = np.abs(fft_result_positive) / samples_num

    # 找到峰值
    max_idx = np.argmax(fft_magnitude)
    peak_freq = fft_freqs_positive[max_idx]
    peak_magnitude = fft_magnitude[max_idx]

    print(f"  FFT峰值频率: {peak_freq:.6f} Hz")
    print(f"  FFT峰值幅度: {peak_magnitude:.6f} (应为 {true_amplitude/2:.6f})")
    print(f"  FFT频率误差: {abs(peak_freq - true_frequency):.6f} Hz")
    print()

    # 使用Hann窗的FFT分析
    print("Hann窗FFT分析:")
    from scipy.signal.windows import hann

    hann_window = hann(samples_num, sym=False)
    windowed_signal = test_waveform * hann_window

    fft_result_windowed = np.fft.fft(windowed_signal)
    fft_result_windowed_positive = fft_result_windowed[positive_freq_mask]

    # 窗函数补偿
    window_coherent_gain = np.mean(hann_window)
    fft_magnitude_windowed = np.abs(fft_result_windowed_positive) / (
        samples_num * window_coherent_gain
    )

    # 找到峰值
    max_idx_windowed = np.argmax(fft_magnitude_windowed)
    peak_freq_windowed = fft_freqs_positive[max_idx_windowed]
    peak_magnitude_windowed = fft_magnitude_windowed[max_idx_windowed]

    print(f"  窗函数相干增益: {window_coherent_gain:.6f}")
    print(f"  Hann窗FFT峰值频率: {peak_freq_windowed:.6f} Hz")
    print(
        f"  Hann窗FFT峰值幅度: {peak_magnitude_windowed:.6f} (应为 {true_amplitude/2:.6f})"
    )
    print(f"  Hann窗FFT频率误差: {abs(peak_freq_windowed - true_frequency):.6f} Hz")

    # 测试抛物线插值
    print()
    print("抛物线插值测试:")
    if max_idx_windowed > 0 and max_idx_windowed < len(fft_magnitude_windowed) - 1:
        y1 = fft_magnitude_windowed[max_idx_windowed - 1]
        y2 = fft_magnitude_windowed[max_idx_windowed]
        y3 = fft_magnitude_windowed[max_idx_windowed + 1]

        print(f"  y1={y1:.6f}, y2={y2:.6f}, y3={y3:.6f}")

        denominator = y1 - 2 * y2 + y3
        print(f"  分母 (y1-2*y2+y3): {denominator:.10f}")

        if abs(denominator) > 1e-12:
            delta = 0.5 * (y1 - y3) / denominator
            freq_resolution = sampling_rate / samples_num
            refined_freq = peak_freq_windowed + delta * freq_resolution
            refined_magnitude = y2 - 0.25 * (y1 - y3) * delta

            print(f"  delta: {delta:.6f}")
            print(f"  频率分辨率: {freq_resolution:.6f} Hz")
            print(f"  改进后频率: {refined_freq:.6f} Hz")
            print(f"  改进后幅度: {refined_magnitude:.6f}")
            print(f"  改进后峰值幅度: {refined_magnitude * 2:.6f}")
            print(f"  理论峰值幅度: {true_amplitude:.6f}")
            print(f"  幅度误差: {abs(refined_magnitude * 2 - true_amplitude):.6f}")
        else:
            print("  分母接近零，无法进行抛物线插值")

    # 检查理论计算
    print()
    print("理论验证:")
    print(f"  真实信号RMS: {true_amplitude/np.sqrt(2):.6f}")
    print(f"  测量信号RMS: {np.sqrt(np.mean(test_waveform**2)):.6f}")
    print(
        f"  RMS误差: {abs(np.sqrt(np.mean(test_waveform**2)) - true_amplitude/np.sqrt(2)):.6f}"
    )


if __name__ == "__main__":
    debug_estimate_sine_args()
